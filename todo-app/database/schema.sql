-- 创建待办事项表
CREATE TABLE IF NOT EXISTS todos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    completed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为todos表创建更新时间触发器
CREATE TRIGGER update_todos_updated_at 
    BEFORE UPDATE ON todos 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_todos_completed ON todos(completed);
CREATE INDEX IF NOT EXISTS idx_todos_created_at ON todos(created_at DESC);

-- 启用行级安全性（RLS）
ALTER TABLE todos ENABLE ROW LEVEL SECURITY;

-- 创建策略：允许所有操作（在实际应用中，你可能想要更严格的权限控制）
CREATE POLICY "Enable all operations for todos" ON todos
    FOR ALL USING (true);

-- 插入一些示例数据
INSERT INTO todos (title, description, completed) VALUES
    ('学习 Next.js', '完成 Next.js 官方教程', false),
    ('设置 Supabase', '配置数据库和认证', true),
    ('构建待办事项应用', '创建完整的 CRUD 功能', false);
