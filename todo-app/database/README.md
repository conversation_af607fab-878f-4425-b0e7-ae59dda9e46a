# 数据库设置说明

## 在Supabase中设置数据库

### 1. 创建Supabase项目
1. 访问 [Supabase控制台](https://supabase.com)
2. 点击 "New Project"
3. 填写项目信息：
   - Name: `todo-app`
   - Database Password: 设置强密码
   - Region: 选择合适的区域
4. 等待项目创建完成

### 2. 执行数据库脚本
1. 在Supabase控制台中，进入你的项目
2. 点击左侧菜单的 "SQL Editor"
3. 复制 `schema.sql` 文件中的内容
4. 粘贴到SQL编辑器中
5. 点击 "Run" 执行脚本

### 3. 获取项目配置
1. 在Supabase控制台中，点击左侧菜单的 "Settings"
2. 点击 "API"
3. 复制以下信息：
   - Project URL
   - anon public key

### 4. 配置环境变量
1. 打开项目根目录的 `.env.local` 文件
2. 替换以下值：
   ```
   NEXT_PUBLIC_SUPABASE_URL=你的项目URL
   NEXT_PUBLIC_SUPABASE_ANON_KEY=你的anon_key
   ```

## 数据库表结构

### todos 表
- `id`: UUID (主键，自动生成)
- `title`: VARCHAR(255) (必填，待办事项标题)
- `description`: TEXT (可选，详细描述)
- `completed`: BOOLEAN (默认false，完成状态)
- `created_at`: TIMESTAMP (自动生成，创建时间)
- `updated_at`: TIMESTAMP (自动更新，修改时间)

### 特性
- 自动更新 `updated_at` 字段
- 行级安全性（RLS）已启用
- 包含性能优化索引
- 预置示例数据
