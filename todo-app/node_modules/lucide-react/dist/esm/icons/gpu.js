/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M2 21V3", key: "1bzk4w" }],
  ["path", { d: "M2 5h18a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H2.26", key: "1d64pi" }],
  ["path", { d: "M7 17v3a1 1 0 0 0 1 1h5a1 1 0 0 0 1-1v-3", key: "5hbqbf" }],
  ["circle", { cx: "16", cy: "11", r: "2", key: "qt15rb" }],
  ["circle", { cx: "8", cy: "11", r: "2", key: "ssideg" }]
];
const Gpu = createLucideIcon("gpu", __iconNode);

export { __iconNode, Gpu as default };
//# sourceMappingURL=gpu.js.map
