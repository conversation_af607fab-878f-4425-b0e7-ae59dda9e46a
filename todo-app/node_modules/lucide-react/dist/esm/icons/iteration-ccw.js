/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m16 14 4 4-4 4", key: "hkso8o" }],
  ["path", { d: "M20 10a8 8 0 1 0-8 8h8", key: "1bik7b" }]
];
const IterationCcw = createLucideIcon("iteration-ccw", __iconNode);

export { __iconNode, IterationCcw as default };
//# sourceMappingURL=iteration-ccw.js.map
