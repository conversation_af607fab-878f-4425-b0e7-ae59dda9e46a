/**
 * @license lucide-react v0.523.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M19.929 18.629A1 1 0 0 1 19 20H9a1 1 0 0 1-.928-1.371l2-5A1 1 0 0 1 11 13h6a1 1 0 0 1 .928.629z",
      key: "u4w2d7"
    }
  ],
  [
    "path",
    { d: "M6 3a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2H5a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1z", key: "15356w" }
  ],
  ["path", { d: "M8 6h4a2 2 0 0 1 2 2v5", key: "1m6m7x" }]
];
const LampWallDown = createLucideIcon("lamp-wall-down", __iconNode);

export { __iconNode, LampWallDown as default };
//# sourceMappingURL=lamp-wall-down.js.map
