# 待办事项管理应用 (Todo List App)

一个使用 Next.js 14、TypeScript、Tailwind CSS 和 Supabase 构建的现代化待办事项管理应用。

## ✨ 功能特性

- ✅ **完整的 CRUD 操作**：创建、读取、更新、删除待办事项
- 🎯 **状态管理**：标记待办事项为完成/未完成
- 🔍 **智能过滤**：按状态筛选待办事项（全部/待完成/已完成）
- 📊 **统计信息**：实时显示待办事项统计
- 🎨 **现代化 UI**：使用 Tailwind CSS 构建的响应式界面
- 💾 **数据持久化**：使用 Supabase 进行数据存储
- 🔄 **实时更新**：支持数据的实时同步
- 📱 **响应式设计**：适配各种设备屏幕

## 🛠️ 技术栈

- **前端框架**: Next.js 14 (App Router)
- **编程语言**: TypeScript
- **样式框架**: Tailwind CSS
- **数据库**: Supabase (PostgreSQL)
- **表单处理**: React Hook Form + Zod
- **图标库**: Lucide React
- **状态管理**: React Hooks

## 📁 项目结构

```
todo-app/
├── src/
│   ├── app/                 # Next.js App Router 页面
│   │   ├── globals.css      # 全局样式
│   │   ├── layout.tsx       # 根布局
│   │   └── page.tsx         # 主页面
│   ├── components/          # React 组件
│   │   ├── AddTodoForm.tsx  # 添加待办事项表单
│   │   ├── TodoItem.tsx     # 单个待办事项组件
│   │   └── TodoList.tsx     # 待办事项列表组件
│   ├── hooks/               # 自定义 Hooks
│   │   └── useTodos.ts      # 待办事项数据管理
│   ├── lib/                 # 工具库
│   │   └── supabase.ts      # Supabase 客户端配置
│   └── types/               # TypeScript 类型定义
│       └── todo.ts          # 待办事项类型
├── database/                # 数据库相关文件
│   ├── schema.sql           # 数据库表结构
│   └── README.md            # 数据库设置说明
├── .env.local               # 环境变量配置
└── package.json             # 项目依赖
```

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd todo-app
```

### 2. 安装依赖

```bash
npm install
```

### 3. 配置环境变量

复制 `.env.local` 文件并填入你的 Supabase 项目信息：

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=你的_supabase_项目_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=你的_supabase_anon_key
```

### 4. 设置数据库

1. 在 [Supabase](https://supabase.com) 创建新项目
2. 在 SQL 编辑器中执行 `database/schema.sql` 中的脚本
3. 获取项目 URL 和 API Key 并更新环境变量

详细步骤请参考 `database/README.md`

### 5. 启动开发服务器

```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

## 📊 数据库设计

### todos 表结构

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | UUID | 主键，自动生成 |
| title | VARCHAR(255) | 待办事项标题（必填） |
| description | TEXT | 详细描述（可选） |
| completed | BOOLEAN | 完成状态，默认 false |
| created_at | TIMESTAMP | 创建时间，自动生成 |
| updated_at | TIMESTAMP | 更新时间，自动更新 |

### 特性

- 自动更新 `updated_at` 字段
- 行级安全性（RLS）已启用
- 包含性能优化索引
- 预置示例数据

## 🎯 主要功能

### 1. 添加待办事项
- 点击"添加新的待办事项"按钮
- 填写标题（必填）和描述（可选）
- 支持表单验证

### 2. 管理待办事项
- 点击复选框标记完成/未完成
- 点击编辑图标修改内容
- 点击删除图标移除事项

### 3. 过滤和统计
- 使用过滤按钮查看不同状态的事项
- 实时显示统计信息
- 批量删除已完成事项

## 🔧 开发说明

### 演示模式

如果没有配置 Supabase，应用会自动进入演示模式：
- 使用本地模拟数据
- 所有功能正常工作
- 数据不会持久化
- 页面顶部会显示配置提示

### 自定义样式

项目使用 Tailwind CSS，你可以：
- 修改 `tailwind.config.ts` 自定义主题
- 在组件中直接使用 Tailwind 类名
- 在 `globals.css` 中添加全局样式

### 添加新功能

1. 在 `src/types/todo.ts` 中更新类型定义
2. 修改 `database/schema.sql` 更新数据库结构
3. 在 `src/hooks/useTodos.ts` 中添加新的数据操作
4. 创建或修改相关组件

## 📝 脚本命令

```bash
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run start        # 启动生产服务器
npm run lint         # 运行 ESLint 检查
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Next.js](https://nextjs.org/) - React 框架
- [Supabase](https://supabase.com/) - 开源 Firebase 替代方案
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架
- [Lucide](https://lucide.dev/) - 图标库
