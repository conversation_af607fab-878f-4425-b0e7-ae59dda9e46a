# Next.js 待办事项管理应用 - 技术文档

## 📋 项目概述

### 应用描述
这是一个基于 Next.js 和 Supabase 构建的现代化待办事项管理应用，提供完整的任务管理功能，包括创建、编辑、删除和状态管理。

### 目标用户
- 个人用户：需要管理日常任务和提高工作效率
- 小团队：简单的任务协作和进度跟踪
- 开发者：作为学习现代全栈开发的参考项目

### 主要特性
- ✅ 完整的 CRUD 操作（创建、读取、更新、删除）
- ✅ 实时数据同步
- ✅ 响应式 UI 设计
- ✅ 任务状态管理（待完成/已完成）
- ✅ 智能过滤功能
- ✅ 统计信息展示
- ✅ 优雅的加载状态
- ✅ 错误处理机制

## 🏗️ 技术架构

### 前端技术栈
- **框架**: Next.js 15.3.4 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **UI组件**: 自定义组件 + Lucide React 图标
- **状态管理**: React Hooks (useState, useEffect)
- **HTTP客户端**: Supabase JavaScript SDK

### 后端服务
- **数据库**: Supabase PostgreSQL
- **认证**: Supabase Auth (预留)
- **实时功能**: Supabase Realtime (预留)
- **API**: Supabase REST API

### 部署平台
- **前端部署**: Vercel
- **数据库托管**: Supabase Cloud
- **域名**: Vercel 自动生成域名

### 开发工具
- **包管理**: npm
- **代码规范**: ESLint + TypeScript
- **版本控制**: Git
- **IDE**: 支持 TypeScript 的现代编辑器

## 📁 项目结构

```
todo-app/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── globals.css         # 全局样式
│   │   ├── layout.tsx          # 根布局组件
│   │   └── page.tsx            # 主页面组件
│   ├── components/             # React 组件
│   │   ├── AddTodoForm.tsx     # 添加待办事项表单
│   │   ├── TodoItem.tsx        # 单个待办事项组件
│   │   ├── TodoList.tsx        # 待办事项列表组件
│   │   └── __tests__/          # 组件测试文件
│   ├── hooks/                  # 自定义 React Hooks
│   │   └── useTodos.ts         # 待办事项数据管理 Hook
│   ├── lib/                    # 工具库
│   │   └── supabase.ts         # Supabase 客户端配置
│   └── types/                  # TypeScript 类型定义
│       └── todo.ts             # 待办事项类型定义
├── database/                   # 数据库相关文件
│   ├── README.md               # 数据库说明
│   └── schema.sql              # 数据库表结构
├── public/                     # 静态资源
├── .env.local                  # 环境变量配置
├── .gitignore                  # Git 忽略文件
├── next.config.js              # Next.js 配置
├── package.json                # 项目依赖
├── tailwind.config.ts          # Tailwind CSS 配置
├── tsconfig.json               # TypeScript 配置
└── vercel.json                 # Vercel 部署配置
```

## 🔧 核心功能实现

### CRUD 操作

#### 1. 创建待办事项 (Create)
```typescript
const addTodo = async (input: CreateTodoInput) => {
  const { data, error } = await supabase
    .from('todos')
    .insert([{
      title: input.title,
      description: input.description || null,
      completed: false
    }])
    .select()
  
  if (error) throw error
  return data[0]
}
```

#### 2. 读取待办事项 (Read)
```typescript
const fetchTodos = async () => {
  const { data, error } = await supabase
    .from('todos')
    .select('*')
    .order('created_at', { ascending: false })
  
  if (error) throw error
  return data
}
```

#### 3. 更新待办事项 (Update)
```typescript
const updateTodo = async (id: string, updates: Partial<Todo>) => {
  const { data, error } = await supabase
    .from('todos')
    .update(updates)
    .eq('id', id)
    .select()
  
  if (error) throw error
  return data[0]
}
```

#### 4. 删除待办事项 (Delete)
```typescript
const deleteTodo = async (id: string) => {
  const { error } = await supabase
    .from('todos')
    .delete()
    .eq('id', id)
  
  if (error) throw error
}
```

### 数据同步机制
- 使用 React 的 `useEffect` Hook 在组件挂载时获取数据
- 所有 CRUD 操作完成后立即更新本地状态
- 错误处理确保数据一致性

### 过滤功能
```typescript
const filteredTodos = useMemo(() => {
  switch (filter) {
    case 'completed':
      return todos.filter(todo => todo.completed)
    case 'pending':
      return todos.filter(todo => !todo.completed)
    default:
      return todos
  }
}, [todos, filter])
```

## 🗄️ 数据库设计

### todos 表结构
```sql
CREATE TABLE todos (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  completed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 字段说明
- `id`: 主键，UUID 类型，自动生成
- `title`: 待办事项标题，必填
- `description`: 待办事项描述，可选
- `completed`: 完成状态，布尔值，默认 false
- `created_at`: 创建时间，自动生成
- `updated_at`: 更新时间，自动生成

### 索引优化
```sql
-- 按创建时间排序的索引
CREATE INDEX idx_todos_created_at ON todos(created_at DESC);

-- 按完成状态过滤的索引
CREATE INDEX idx_todos_completed ON todos(completed);
```

## 🔌 API 接口

### Supabase REST API 端点

#### 获取所有待办事项
```
GET /rest/v1/todos
Authorization: Bearer [ANON_KEY]
```

#### 创建新待办事项
```
POST /rest/v1/todos
Content-Type: application/json
Authorization: Bearer [ANON_KEY]

{
  "title": "新的待办事项",
  "description": "描述信息"
}
```

#### 更新待办事项
```
PATCH /rest/v1/todos?id=eq.[TODO_ID]
Content-Type: application/json
Authorization: Bearer [ANON_KEY]

{
  "completed": true
}
```

#### 删除待办事项
```
DELETE /rest/v1/todos?id=eq.[TODO_ID]
Authorization: Bearer [ANON_KEY]
```

## 🚀 部署配置

### Vercel 部署流程

1. **项目初始化**
   ```bash
   vercel login
   vercel --prod
   ```

2. **环境变量配置**
   ```bash
   vercel env add NEXT_PUBLIC_SUPABASE_URL
   vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY
   ```

3. **自动部署**
   - 每次推送到主分支自动触发部署
   - 构建时间约 30-60 秒
   - 支持预览部署

### 环境变量
```env
# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

### 部署 URL
- 生产环境: https://todo-app-hackrabbitqqcoms-projects.vercel.app
- 预览环境: 自动生成的预览链接

## 💻 开发环境设置

### 前置要求
- Node.js 18+ 
- npm 或 yarn
- Git

### 本地开发步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd todo-app
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **配置环境变量**
   ```bash
   cp .env.example .env.local
   # 编辑 .env.local 文件，填入 Supabase 配置
   ```

4. **启动开发服务器**
   ```bash
   npm run dev
   ```

5. **访问应用**
   ```
   http://localhost:3000
   ```

### 开发命令
```bash
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run start        # 启动生产服务器
npm run lint         # 代码检查
npm run test         # 运行测试
```
