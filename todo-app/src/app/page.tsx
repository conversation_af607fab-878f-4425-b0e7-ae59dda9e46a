'use client'

import { useTodos } from '@/hooks/useTodos'
import AddTodoForm from '@/components/AddTodoForm'
import TodoList from '@/components/TodoList'
import { Trash2, RefreshCw } from 'lucide-react'

export default function Home() {
  const {
    todos,
    loading,
    error,
    addTodo,
    updateTodo,
    deleteTodo,
    deleteCompletedTodos,
    refetch
  } = useTodos()

  const handleAddTodo = async (input: { title: string; description?: string }) => {
    try {
      await addTodo(input)
    } catch (err) {
      // 错误已在hook中处理
    }
  }

  const handleUpdateTodo = async (id: string, updates: any) => {
    try {
      await updateTodo(id, updates)
    } catch (err) {
      // 错误已在hook中处理
    }
  }

  const handleDeleteTodo = async (id: string) => {
    if (confirm('确定要删除这个待办事项吗？')) {
      try {
        await deleteTodo(id)
      } catch (err) {
        // 错误已在hook中处理
      }
    }
  }

  const handleDeleteCompleted = async () => {
    const completedCount = todos.filter(todo => todo.completed).length
    if (completedCount === 0) {
      alert('没有已完成的待办事项')
      return
    }

    if (confirm(`确定要删除 ${completedCount} 个已完成的待办事项吗？`)) {
      try {
        await deleteCompletedTodos()
      } catch (err) {
        // 错误已在hook中处理
      }
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 头部 */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">待办事项管理</h1>
          <p className="text-gray-600">管理你的日常任务，提高工作效率</p>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600">{error}</p>
            <button
              onClick={refetch}
              className="mt-2 text-red-500 hover:text-red-700 underline"
            >
              重试
            </button>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="mb-6 flex gap-2 justify-end">
          <button
            onClick={refetch}
            className="flex items-center gap-1 px-3 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            <RefreshCw size={16} />
            刷新
          </button>
          <button
            onClick={handleDeleteCompleted}
            className="flex items-center gap-1 px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
          >
            <Trash2 size={16} />
            清除已完成
          </button>
        </div>

        {/* 添加待办事项表单 */}
        <div className="mb-6">
          <AddTodoForm onAdd={handleAddTodo} />
        </div>

        {/* 待办事项列表 */}
        <TodoList
          todos={todos}
          onUpdate={handleUpdateTodo}
          onDelete={handleDeleteTodo}
        />
      </div>
    </div>
  )
}
