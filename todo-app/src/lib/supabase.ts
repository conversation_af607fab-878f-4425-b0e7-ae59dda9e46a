import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder_key'

// 检查是否为占位符配置
const isPlaceholderConfig = supabaseUrl.includes('placeholder') || supabaseAnonKey.includes('placeholder')

export const supabase = isPlaceholderConfig
  ? null
  : createClient(supabaseUrl, supabaseAnonKey)

export const isSupabaseConfigured = !isPlaceholderConfig

export type Database = {
  public: {
    Tables: {
      todos: {
        Row: {
          id: string
          title: string
          description: string | null
          completed: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          completed?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          completed?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}
