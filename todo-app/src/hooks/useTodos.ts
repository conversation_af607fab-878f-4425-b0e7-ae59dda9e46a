'use client'

import { useState, useEffect } from 'react'
import { supabase, isSupabaseConfigured } from '@/lib/supabase'
import { Todo, CreateTodoInput } from '@/types/todo'

// 模拟数据，用于演示
const mockTodos: Todo[] = [
  {
    id: '1',
    title: '学习 Next.js',
    description: '完成 Next.js 官方教程',
    completed: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: '2',
    title: '设置 Supabase',
    description: '配置数据库和认证',
    completed: true,
    created_at: new Date(Date.now() - 86400000).toISOString(),
    updated_at: new Date(Date.now() - 86400000).toISOString()
  },
  {
    id: '3',
    title: '构建待办事项应用',
    description: '创建完整的 CRUD 功能',
    completed: false,
    created_at: new Date(Date.now() - 172800000).toISOString(),
    updated_at: new Date(Date.now() - 172800000).toISOString()
  }
]

export function useTodos() {
  const [todos, setTodos] = useState<Todo[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 获取所有待办事项
  const fetchTodos = async () => {
    try {
      setLoading(true)
      setError(null)

      if (!isSupabaseConfigured) {
        // 使用模拟数据
        await new Promise(resolve => setTimeout(resolve, 500)) // 模拟网络延迟
        setTodos(mockTodos)
        setError('演示模式：请配置 Supabase 以使用真实数据库')
        return
      }

      if (!supabase) {
        throw new Error('Supabase 客户端未初始化')
      }

      const { data, error } = await supabase
        .from('todos')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error

      setTodos(data || [])
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取待办事项失败')
      console.error('Error fetching todos:', err)
    } finally {
      setLoading(false)
    }
  }

  // 添加新的待办事项
  const addTodo = async (input: CreateTodoInput) => {
    try {
      setError(null)

      if (!isSupabaseConfigured) {
        // 模拟添加
        const newTodo: Todo = {
          id: Date.now().toString(),
          title: input.title,
          description: input.description || null,
          completed: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
        setTodos(prev => [newTodo, ...prev])
        return newTodo
      }

      if (!supabase) {
        throw new Error('Supabase 客户端未初始化')
      }

      const { data, error } = await supabase
        .from('todos')
        .insert([input])
        .select()
        .single()

      if (error) throw error

      setTodos(prev => [data, ...prev])
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : '添加待办事项失败')
      console.error('Error adding todo:', err)
      throw err
    }
  }

  // 更新待办事项
  const updateTodo = async (id: string, updates: Partial<Todo>) => {
    try {
      setError(null)

      if (!isSupabaseConfigured) {
        // 模拟更新
        const updatedTodo = { ...updates, updated_at: new Date().toISOString() }
        setTodos(prev => prev.map(todo =>
          todo.id === id ? { ...todo, ...updatedTodo } : todo
        ))
        return updatedTodo
      }

      if (!supabase) {
        throw new Error('Supabase 客户端未初始化')
      }

      const { data, error } = await supabase
        .from('todos')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error

      setTodos(prev => prev.map(todo =>
        todo.id === id ? { ...todo, ...data } : todo
      ))
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新待办事项失败')
      console.error('Error updating todo:', err)
      throw err
    }
  }

  // 删除待办事项
  const deleteTodo = async (id: string) => {
    try {
      setError(null)

      if (!isSupabaseConfigured) {
        // 模拟删除
        setTodos(prev => prev.filter(todo => todo.id !== id))
        return
      }

      if (!supabase) {
        throw new Error('Supabase 客户端未初始化')
      }

      const { error } = await supabase
        .from('todos')
        .delete()
        .eq('id', id)

      if (error) throw error

      setTodos(prev => prev.filter(todo => todo.id !== id))
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除待办事项失败')
      console.error('Error deleting todo:', err)
      throw err
    }
  }

  // 批量删除已完成的待办事项
  const deleteCompletedTodos = async () => {
    try {
      setError(null)

      if (!isSupabaseConfigured) {
        // 模拟删除
        setTodos(prev => prev.filter(todo => !todo.completed))
        return
      }

      if (!supabase) {
        throw new Error('Supabase 客户端未初始化')
      }

      const { error } = await supabase
        .from('todos')
        .delete()
        .eq('completed', true)

      if (error) throw error

      setTodos(prev => prev.filter(todo => !todo.completed))
    } catch (err) {
      setError(err instanceof Error ? err.message : '删除已完成事项失败')
      console.error('Error deleting completed todos:', err)
      throw err
    }
  }

  // 组件挂载时获取数据
  useEffect(() => {
    fetchTodos()
  }, [])

  return {
    todos,
    loading,
    error,
    addTodo,
    updateTodo,
    deleteTodo,
    deleteCompletedTodos,
    refetch: fetchTodos
  }
}
