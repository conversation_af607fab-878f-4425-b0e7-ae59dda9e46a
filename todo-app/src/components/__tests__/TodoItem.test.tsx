import { render, screen, fireEvent } from '@testing-library/react'
import TodoItem from '../TodoItem'
import { Todo } from '@/types/todo'

const mockTodo: Todo = {
  id: '1',
  title: '测试待办事项',
  description: '这是一个测试描述',
  completed: false,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

const mockOnUpdate = jest.fn()
const mockOnDelete = jest.fn()

describe('TodoItem', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('应该渲染待办事项内容', () => {
    render(
      <TodoItem
        todo={mockTodo}
        onUpdate={mockOnUpdate}
        onDelete={mockOnDelete}
      />
    )

    expect(screen.getByText('测试待办事项')).toBeInTheDocument()
    expect(screen.getByText('这是一个测试描述')).toBeInTheDocument()
  })

  it('应该在点击复选框时切换完成状态', () => {
    render(
      <TodoItem
        todo={mockTodo}
        onUpdate={mockOnUpdate}
        onDelete={mockOnDelete}
      />
    )

    const checkbox = screen.getByRole('button', { name: /toggle/i })
    fireEvent.click(checkbox)

    expect(mockOnUpdate).toHaveBeenCalledWith('1', { completed: true })
  })

  it('应该在点击删除按钮时调用删除函数', () => {
    render(
      <TodoItem
        todo={mockTodo}
        onUpdate={mockOnUpdate}
        onDelete={mockOnDelete}
      />
    )

    const deleteButton = screen.getByRole('button', { name: /delete/i })
    fireEvent.click(deleteButton)

    expect(mockOnDelete).toHaveBeenCalledWith('1')
  })

  it('应该在编辑模式下显示输入框', () => {
    render(
      <TodoItem
        todo={mockTodo}
        onUpdate={mockOnUpdate}
        onDelete={mockOnDelete}
      />
    )

    const editButton = screen.getByRole('button', { name: /edit/i })
    fireEvent.click(editButton)

    expect(screen.getByDisplayValue('测试待办事项')).toBeInTheDocument()
    expect(screen.getByDisplayValue('这是一个测试描述')).toBeInTheDocument()
  })
})
