'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Plus } from 'lucide-react'
import { CreateTodoInput } from '@/types/todo'

const todoSchema = z.object({
  title: z.string().min(1, '标题不能为空').max(255, '标题不能超过255个字符'),
  description: z.string().optional()
})

interface AddTodoFormProps {
  onAdd: (todo: CreateTodoInput) => void
  isLoading?: boolean
}

export default function AddTodoForm({ onAdd, isLoading }: AddTodoFormProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<CreateTodoInput>({
    resolver: zodResolver(todoSchema)
  })

  const onSubmit = (data: CreateTodoInput) => {
    onAdd(data)
    reset()
    setIsExpanded(false)
  }

  const handleCancel = () => {
    reset()
    setIsExpanded(false)
  }

  if (!isExpanded) {
    return (
      <button
        onClick={() => setIsExpanded(true)}
        className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-500 hover:border-blue-500 hover:text-blue-500 transition-colors flex items-center justify-center gap-2"
      >
        <Plus size={20} />
        添加新的待办事项
      </button>
    )
  }

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div>
          <input
            {...register('title')}
            type="text"
            placeholder="待办事项标题"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            autoFocus
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-500">{errors.title.message}</p>
          )}
        </div>
        
        <div>
          <textarea
            {...register('description')}
            placeholder="描述（可选）"
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.description && (
            <p className="mt-1 text-sm text-red-500">{errors.description.message}</p>
          )}
        </div>
        
        <div className="flex gap-2">
          <button
            type="submit"
            disabled={isLoading}
            className="flex items-center gap-1 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Plus size={16} />
            {isLoading ? '添加中...' : '添加'}
          </button>
          <button
            type="button"
            onClick={handleCancel}
            className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors"
          >
            取消
          </button>
        </div>
      </form>
    </div>
  )
}
