# 部署指南

本文档介绍如何将待办事项管理应用部署到不同的平台。

## 🚀 Vercel 部署（推荐）

Vercel 是 Next.js 的官方部署平台，提供最佳的性能和开发体验。

### 步骤

1. **准备代码**
   ```bash
   git add .
   git commit -m "Ready for deployment"
   git push origin main
   ```

2. **连接 Vercel**
   - 访问 [vercel.com](https://vercel.com)
   - 使用 GitHub 账号登录
   - 点击 "New Project"
   - 选择你的 todo-app 仓库

3. **配置环境变量**
   在 Vercel 项目设置中添加：
   ```
   NEXT_PUBLIC_SUPABASE_URL=你的_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=你的_supabase_anon_key
   ```

4. **部署**
   - 点击 "Deploy"
   - 等待构建完成
   - 访问提供的 URL

### 自动部署

每次推送到 main 分支时，Vercel 会自动重新部署应用。

## 🐳 Docker 部署

### 创建 Dockerfile

```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

### 构建和运行

```bash
# 构建镜像
docker build -t todo-app .

# 运行容器
docker run -p 3000:3000 \
  -e NEXT_PUBLIC_SUPABASE_URL=你的_supabase_url \
  -e NEXT_PUBLIC_SUPABASE_ANON_KEY=你的_supabase_anon_key \
  todo-app
```

## ☁️ 其他平台部署

### Netlify

1. 连接 GitHub 仓库
2. 设置构建命令：`npm run build`
3. 设置发布目录：`out`
4. 在 `next.config.js` 中添加：
   ```javascript
   /** @type {import('next').NextConfig} */
   const nextConfig = {
     output: 'export',
     trailingSlash: true,
     images: {
       unoptimized: true
     }
   }
   module.exports = nextConfig
   ```

### Railway

1. 连接 GitHub 仓库
2. 添加环境变量
3. Railway 会自动检测 Next.js 项目并部署

### DigitalOcean App Platform

1. 创建新应用
2. 连接 GitHub 仓库
3. 配置环境变量
4. 部署

## 🔧 生产环境优化

### 性能优化

1. **启用压缩**
   ```javascript
   // next.config.js
   const nextConfig = {
     compress: true,
   }
   ```

2. **图片优化**
   ```javascript
   // next.config.js
   const nextConfig = {
     images: {
       domains: ['your-domain.com'],
       formats: ['image/webp', 'image/avif'],
     },
   }
   ```

3. **Bundle 分析**
   ```bash
   npm install --save-dev @next/bundle-analyzer
   ```

### 安全配置

1. **环境变量**
   - 永远不要在客户端暴露敏感信息
   - 使用 `NEXT_PUBLIC_` 前缀的变量会暴露给客户端

2. **CSP 头部**
   ```javascript
   // next.config.js
   const nextConfig = {
     async headers() {
       return [
         {
           source: '/(.*)',
           headers: [
             {
               key: 'Content-Security-Policy',
               value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline';"
             }
           ]
         }
       ]
     }
   }
   ```

### 监控和分析

1. **Vercel Analytics**
   ```bash
   npm install @vercel/analytics
   ```

2. **错误监控**
   - 集成 Sentry 或其他错误监控服务
   - 设置日志记录

## 🔍 部署检查清单

- [ ] 环境变量已正确配置
- [ ] Supabase 数据库已设置
- [ ] 构建过程无错误
- [ ] 所有功能在生产环境中正常工作
- [ ] 性能优化已应用
- [ ] 安全配置已设置
- [ ] 监控和分析已配置
- [ ] 备份策略已制定

## 🆘 常见问题

### 构建失败

1. 检查 Node.js 版本（推荐 18+）
2. 清除缓存：`npm run clean` 或 `rm -rf .next`
3. 重新安装依赖：`rm -rf node_modules && npm install`

### 环境变量问题

1. 确保变量名正确
2. 客户端变量必须以 `NEXT_PUBLIC_` 开头
3. 重启开发服务器

### Supabase 连接问题

1. 检查 URL 和 API Key 是否正确
2. 确认 Supabase 项目状态
3. 检查网络连接和防火墙设置

## 📞 支持

如果遇到部署问题，请：

1. 检查构建日志
2. 查看浏览器控制台错误
3. 参考平台官方文档
4. 在项目仓库中创建 Issue
