{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/supabase/todo-app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder_key'\n\n// 检查是否为占位符配置\nconst isPlaceholderConfig = supabaseUrl.includes('placeholder') || supabaseAnonKey.includes('placeholder')\n\nexport const supabase = isPlaceholderConfig\n  ? null\n  : createClient(supabaseUrl, supabaseAnonKey)\n\nexport const isSupabaseConfigured = !isPlaceholderConfig\n\nexport type Database = {\n  public: {\n    Tables: {\n      todos: {\n        Row: {\n          id: string\n          title: string\n          description: string | null\n          completed: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          title: string\n          description?: string | null\n          completed?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          title?: string\n          description?: string | null\n          completed?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAEoB;AAFpB;;AAEA,MAAM,cAAc,0DAAwC;AAC5D,MAAM,kBAAkB,8DAA6C;AAErE,aAAa;AACb,MAAM,sBAAsB,YAAY,QAAQ,CAAC,kBAAkB,gBAAgB,QAAQ,CAAC;AAErF,MAAM,WAAW,sBACpB,OACA,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAEvB,MAAM,uBAAuB,CAAC", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/supabase/todo-app/src/hooks/useTodos.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { supabase, isSupabaseConfigured } from '@/lib/supabase'\nimport { Todo, CreateTodoInput, UpdateTodoInput } from '@/types/todo'\n\n// 模拟数据，用于演示\nconst mockTodos: Todo[] = [\n  {\n    id: '1',\n    title: '学习 Next.js',\n    description: '完成 Next.js 官方教程',\n    completed: false,\n    created_at: new Date().toISOString(),\n    updated_at: new Date().toISOString()\n  },\n  {\n    id: '2',\n    title: '设置 Supabase',\n    description: '配置数据库和认证',\n    completed: true,\n    created_at: new Date(Date.now() - 86400000).toISOString(),\n    updated_at: new Date(Date.now() - 86400000).toISOString()\n  },\n  {\n    id: '3',\n    title: '构建待办事项应用',\n    description: '创建完整的 CRUD 功能',\n    completed: false,\n    created_at: new Date(Date.now() - 172800000).toISOString(),\n    updated_at: new Date(Date.now() - 172800000).toISOString()\n  }\n]\n\nexport function useTodos() {\n  const [todos, setTodos] = useState<Todo[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  // 获取所有待办事项\n  const fetchTodos = async () => {\n    try {\n      setLoading(true)\n      setError(null)\n\n      if (!isSupabaseConfigured) {\n        // 使用模拟数据\n        await new Promise(resolve => setTimeout(resolve, 500)) // 模拟网络延迟\n        setTodos(mockTodos)\n        setError('演示模式：请配置 Supabase 以使用真实数据库')\n        return\n      }\n\n      if (!supabase) {\n        throw new Error('Supabase 客户端未初始化')\n      }\n\n      const { data, error } = await supabase\n        .from('todos')\n        .select('*')\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n\n      setTodos(data || [])\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '获取待办事项失败')\n      console.error('Error fetching todos:', err)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // 添加新的待办事项\n  const addTodo = async (input: CreateTodoInput) => {\n    try {\n      setError(null)\n\n      if (!isSupabaseConfigured) {\n        // 模拟添加\n        const newTodo: Todo = {\n          id: Date.now().toString(),\n          title: input.title,\n          description: input.description || null,\n          completed: false,\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        }\n        setTodos(prev => [newTodo, ...prev])\n        return newTodo\n      }\n\n      if (!supabase) {\n        throw new Error('Supabase 客户端未初始化')\n      }\n\n      const { data, error } = await supabase\n        .from('todos')\n        .insert([input])\n        .select()\n        .single()\n\n      if (error) throw error\n\n      setTodos(prev => [data, ...prev])\n      return data\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '添加待办事项失败')\n      console.error('Error adding todo:', err)\n      throw err\n    }\n  }\n\n  // 更新待办事项\n  const updateTodo = async (id: string, updates: Partial<Todo>) => {\n    try {\n      setError(null)\n\n      if (!isSupabaseConfigured) {\n        // 模拟更新\n        const updatedTodo = { ...updates, updated_at: new Date().toISOString() }\n        setTodos(prev => prev.map(todo =>\n          todo.id === id ? { ...todo, ...updatedTodo } : todo\n        ))\n        return updatedTodo\n      }\n\n      if (!supabase) {\n        throw new Error('Supabase 客户端未初始化')\n      }\n\n      const { data, error } = await supabase\n        .from('todos')\n        .update(updates)\n        .eq('id', id)\n        .select()\n        .single()\n\n      if (error) throw error\n\n      setTodos(prev => prev.map(todo =>\n        todo.id === id ? { ...todo, ...data } : todo\n      ))\n      return data\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '更新待办事项失败')\n      console.error('Error updating todo:', err)\n      throw err\n    }\n  }\n\n  // 删除待办事项\n  const deleteTodo = async (id: string) => {\n    try {\n      setError(null)\n\n      if (!isSupabaseConfigured) {\n        // 模拟删除\n        setTodos(prev => prev.filter(todo => todo.id !== id))\n        return\n      }\n\n      if (!supabase) {\n        throw new Error('Supabase 客户端未初始化')\n      }\n\n      const { error } = await supabase\n        .from('todos')\n        .delete()\n        .eq('id', id)\n\n      if (error) throw error\n\n      setTodos(prev => prev.filter(todo => todo.id !== id))\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '删除待办事项失败')\n      console.error('Error deleting todo:', err)\n      throw err\n    }\n  }\n\n  // 批量删除已完成的待办事项\n  const deleteCompletedTodos = async () => {\n    try {\n      setError(null)\n\n      if (!isSupabaseConfigured) {\n        // 模拟删除\n        setTodos(prev => prev.filter(todo => !todo.completed))\n        return\n      }\n\n      if (!supabase) {\n        throw new Error('Supabase 客户端未初始化')\n      }\n\n      const { error } = await supabase\n        .from('todos')\n        .delete()\n        .eq('completed', true)\n\n      if (error) throw error\n\n      setTodos(prev => prev.filter(todo => !todo.completed))\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '删除已完成事项失败')\n      console.error('Error deleting completed todos:', err)\n      throw err\n    }\n  }\n\n  // 组件挂载时获取数据\n  useEffect(() => {\n    fetchTodos()\n  }, [])\n\n  return {\n    todos,\n    loading,\n    error,\n    addTodo,\n    updateTodo,\n    deleteTodo,\n    deleteCompletedTodos,\n    refetch: fetchTodos\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAMA,YAAY;AACZ,MAAM,YAAoB;IACxB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,YAAY,IAAI,OAAO,WAAW;QAClC,YAAY,IAAI,OAAO,WAAW;IACpC;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,UAAU,WAAW;QACvD,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,UAAU,WAAW;IACzD;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,WAAW;QACX,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,WAAW,WAAW;QACxD,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,WAAW,WAAW;IAC1D;CACD;AAEM,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,WAAW;IACX,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,SAAS;YAET,IAAI,CAAC,yHAAA,CAAA,uBAAoB,EAAE;gBACzB,SAAS;gBACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,MAAM,SAAS;;gBAChE,SAAS;gBACT,SAAS;gBACT;YACF;YAEA,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YAEjB,SAAS,QAAQ,EAAE;QACrB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,WAAW;IACX,MAAM,UAAU,OAAO;QACrB,IAAI;YACF,SAAS;YAET,IAAI,CAAC,yHAAA,CAAA,uBAAoB,EAAE;gBACzB,OAAO;gBACP,MAAM,UAAgB;oBACpB,IAAI,KAAK,GAAG,GAAG,QAAQ;oBACvB,OAAO,MAAM,KAAK;oBAClB,aAAa,MAAM,WAAW,IAAI;oBAClC,WAAW;oBACX,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;gBACpC;gBACA,SAAS,CAAA,OAAQ;wBAAC;2BAAY;qBAAK;gBACnC,OAAO;YACT;YAEA,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC;gBAAC;aAAM,EACd,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,SAAS,CAAA,OAAQ;oBAAC;uBAAS;iBAAK;YAChC,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR;IACF;IAEA,SAAS;IACT,MAAM,aAAa,OAAO,IAAY;QACpC,IAAI;YACF,SAAS;YAET,IAAI,CAAC,yHAAA,CAAA,uBAAoB,EAAE;gBACzB,OAAO;gBACP,MAAM,cAAc;oBAAE,GAAG,OAAO;oBAAE,YAAY,IAAI,OAAO,WAAW;gBAAG;gBACvE,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,KAAK;4BAAE,GAAG,IAAI;4BAAE,GAAG,WAAW;wBAAC,IAAI;gBAEjD,OAAO;YACT;YAEA,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,KAAK;wBAAE,GAAG,IAAI;wBAAE,GAAG,IAAI;oBAAC,IAAI;YAE1C,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,SAAS;IACT,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,SAAS;YAET,IAAI,CAAC,yHAAA,CAAA,uBAAoB,EAAE;gBACzB,OAAO;gBACP,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBACjD;YACF;YAEA,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,GACN,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO,MAAM;YAEjB,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACnD,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,eAAe;IACf,MAAM,uBAAuB;QAC3B,IAAI;YACF,SAAS;YAET,IAAI,CAAC,yHAAA,CAAA,uBAAoB,EAAE;gBACzB,OAAO;gBACP,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS;gBACpD;YACF;YAEA,IAAI,CAAC,yHAAA,CAAA,WAAQ,EAAE;gBACb,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,GACN,EAAE,CAAC,aAAa;YAEnB,IAAI,OAAO,MAAM;YAEjB,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS;QACtD,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA,YAAY;IACZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA,SAAS;IACX;AACF;GAhMgB", "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/supabase/todo-app/src/components/AddTodoForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { Plus } from 'lucide-react'\nimport { CreateTodoInput } from '@/types/todo'\n\nconst todoSchema = z.object({\n  title: z.string().min(1, '标题不能为空').max(255, '标题不能超过255个字符'),\n  description: z.string().optional()\n})\n\ninterface AddTodoFormProps {\n  onAdd: (todo: CreateTodoInput) => void\n  isLoading?: boolean\n}\n\nexport default function AddTodoForm({ onAdd, isLoading }: AddTodoFormProps) {\n  const [isExpanded, setIsExpanded] = useState(false)\n  \n  const {\n    register,\n    handleSubmit,\n    reset,\n    formState: { errors }\n  } = useForm<CreateTodoInput>({\n    resolver: zodResolver(todoSchema)\n  })\n\n  const onSubmit = (data: CreateTodoInput) => {\n    onAdd(data)\n    reset()\n    setIsExpanded(false)\n  }\n\n  const handleCancel = () => {\n    reset()\n    setIsExpanded(false)\n  }\n\n  if (!isExpanded) {\n    return (\n      <button\n        onClick={() => setIsExpanded(true)}\n        className=\"w-full p-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-500 hover:border-blue-500 hover:text-blue-500 transition-colors flex items-center justify-center gap-2\"\n      >\n        <Plus size={20} />\n        添加新的待办事项\n      </button>\n    )\n  }\n\n  return (\n    <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n      <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n        <div>\n          <input\n            {...register('title')}\n            type=\"text\"\n            placeholder=\"待办事项标题\"\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            autoFocus\n          />\n          {errors.title && (\n            <p className=\"mt-1 text-sm text-red-500\">{errors.title.message}</p>\n          )}\n        </div>\n        \n        <div>\n          <textarea\n            {...register('description')}\n            placeholder=\"描述（可选）\"\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          />\n          {errors.description && (\n            <p className=\"mt-1 text-sm text-red-500\">{errors.description.message}</p>\n          )}\n        </div>\n        \n        <div className=\"flex gap-2\">\n          <button\n            type=\"submit\"\n            disabled={isLoading}\n            className=\"flex items-center gap-1 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n          >\n            <Plus size={16} />\n            {isLoading ? '添加中...' : '添加'}\n          </button>\n          <button\n            type=\"button\"\n            onClick={handleCancel}\n            className=\"px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors\"\n          >\n            取消\n          </button>\n        </div>\n      </form>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;;;AANA;;;;;;AASA,MAAM,aAAa,oLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1B,OAAO,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,KAAK;IAC5C,aAAa,oLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAClC;AAOe,SAAS,YAAY,EAAE,KAAK,EAAE,SAAS,EAAoB;;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,CAAC;QAChB,MAAM;QACN;QACA,cAAc;IAChB;IAEA,MAAM,eAAe;QACnB;QACA,cAAc;IAChB;IAEA,IAAI,CAAC,YAAY;QACf,qBACE,6LAAC;YACC,SAAS,IAAM,cAAc;YAC7B,WAAU;;8BAEV,6LAAC,qMAAA,CAAA,OAAI;oBAAC,MAAM;;;;;;gBAAM;;;;;;;IAIxB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAK,UAAU,aAAa;YAAW,WAAU;;8BAChD,6LAAC;;sCACC,6LAAC;4BACE,GAAG,SAAS,QAAQ;4BACrB,MAAK;4BACL,aAAY;4BACZ,WAAU;4BACV,SAAS;;;;;;wBAEV,OAAO,KAAK,kBACX,6LAAC;4BAAE,WAAU;sCAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;8BAIlE,6LAAC;;sCACC,6LAAC;4BACE,GAAG,SAAS,cAAc;4BAC3B,aAAY;4BACZ,MAAM;4BACN,WAAU;;;;;;wBAEX,OAAO,WAAW,kBACjB,6LAAC;4BAAE,WAAU;sCAA6B,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;8BAIxE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;gCACX,YAAY,WAAW;;;;;;;sCAE1B,6LAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAnFwB;;QAQlB,iKAAA,CAAA,UAAO;;;KARW", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/supabase/todo-app/src/components/TodoItem.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Check, Edit2, Trash2, X } from 'lucide-react'\nimport { Todo } from '@/types/todo'\n\ninterface TodoItemProps {\n  todo: Todo\n  onUpdate: (id: string, updates: Partial<Todo>) => void\n  onDelete: (id: string) => void\n}\n\nexport default function TodoItem({ todo, onUpdate, onDelete }: TodoItemProps) {\n  const [isEditing, setIsEditing] = useState(false)\n  const [editTitle, setEditTitle] = useState(todo.title)\n  const [editDescription, setEditDescription] = useState(todo.description || '')\n\n  const handleSave = () => {\n    onUpdate(todo.id, {\n      title: editTitle,\n      description: editDescription || null\n    })\n    setIsEditing(false)\n  }\n\n  const handleCancel = () => {\n    setEditTitle(todo.title)\n    setEditDescription(todo.description || '')\n    setIsEditing(false)\n  }\n\n  const toggleCompleted = () => {\n    onUpdate(todo.id, { completed: !todo.completed })\n  }\n\n  if (isEditing) {\n    return (\n      <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n        <div className=\"space-y-3\">\n          <input\n            type=\"text\"\n            value={editTitle}\n            onChange={(e) => setEditTitle(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            placeholder=\"待办事项标题\"\n          />\n          <textarea\n            value={editDescription}\n            onChange={(e) => setEditDescription(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            placeholder=\"描述（可选）\"\n            rows={3}\n          />\n          <div className=\"flex gap-2\">\n            <button\n              onClick={handleSave}\n              className=\"flex items-center gap-1 px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors\"\n            >\n              <Check size={16} />\n              保存\n            </button>\n            <button\n              onClick={handleCancel}\n              className=\"flex items-center gap-1 px-3 py-1 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors\"\n            >\n              <X size={16} />\n              取消\n            </button>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className={`bg-white p-4 rounded-lg shadow-sm border border-gray-200 ${todo.completed ? 'opacity-75' : ''}`}>\n      <div className=\"flex items-start gap-3\">\n        <button\n          onClick={toggleCompleted}\n          className={`mt-1 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors ${\n            todo.completed\n              ? 'bg-green-500 border-green-500 text-white'\n              : 'border-gray-300 hover:border-green-500'\n          }`}\n        >\n          {todo.completed && <Check size={14} />}\n        </button>\n        \n        <div className=\"flex-1\">\n          <h3 className={`font-medium ${todo.completed ? 'line-through text-gray-500' : 'text-gray-900'}`}>\n            {todo.title}\n          </h3>\n          {todo.description && (\n            <p className={`mt-1 text-sm ${todo.completed ? 'line-through text-gray-400' : 'text-gray-600'}`}>\n              {todo.description}\n            </p>\n          )}\n          <p className=\"mt-2 text-xs text-gray-400\">\n            创建于 {new Date(todo.created_at).toLocaleString('zh-CN')}\n          </p>\n        </div>\n        \n        <div className=\"flex gap-1\">\n          <button\n            onClick={() => setIsEditing(true)}\n            className=\"p-1 text-gray-400 hover:text-blue-500 transition-colors\"\n          >\n            <Edit2 size={16} />\n          </button>\n          <button\n            onClick={() => onDelete(todo.id)}\n            className=\"p-1 text-gray-400 hover:text-red-500 transition-colors\"\n          >\n            <Trash2 size={16} />\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAYe,SAAS,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAiB;;IAC1E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,KAAK;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,WAAW,IAAI;IAE3E,MAAM,aAAa;QACjB,SAAS,KAAK,EAAE,EAAE;YAChB,OAAO;YACP,aAAa,mBAAmB;QAClC;QACA,aAAa;IACf;IAEA,MAAM,eAAe;QACnB,aAAa,KAAK,KAAK;QACvB,mBAAmB,KAAK,WAAW,IAAI;QACvC,aAAa;IACf;IAEA,MAAM,kBAAkB;QACtB,SAAS,KAAK,EAAE,EAAE;YAAE,WAAW,CAAC,KAAK,SAAS;QAAC;IACjD;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wBAC5C,WAAU;wBACV,aAAY;;;;;;kCAEd,6LAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wBAClD,WAAU;wBACV,aAAY;wBACZ,MAAM;;;;;;kCAER,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,uMAAA,CAAA,QAAK;wCAAC,MAAM;;;;;;oCAAM;;;;;;;0CAGrB,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,+LAAA,CAAA,IAAC;wCAAC,MAAM;;;;;;oCAAM;;;;;;;;;;;;;;;;;;;;;;;;IAO3B;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,yDAAyD,EAAE,KAAK,SAAS,GAAG,eAAe,IAAI;kBAC9G,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,SAAS;oBACT,WAAW,CAAC,iFAAiF,EAC3F,KAAK,SAAS,GACV,6CACA,0CACJ;8BAED,KAAK,SAAS,kBAAI,6LAAC,uMAAA,CAAA,QAAK;wBAAC,MAAM;;;;;;;;;;;8BAGlC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAW,CAAC,YAAY,EAAE,KAAK,SAAS,GAAG,+BAA+B,iBAAiB;sCAC5F,KAAK,KAAK;;;;;;wBAEZ,KAAK,WAAW,kBACf,6LAAC;4BAAE,WAAW,CAAC,aAAa,EAAE,KAAK,SAAS,GAAG,+BAA+B,iBAAiB;sCAC5F,KAAK,WAAW;;;;;;sCAGrB,6LAAC;4BAAE,WAAU;;gCAA6B;gCACnC,IAAI,KAAK,KAAK,UAAU,EAAE,cAAc,CAAC;;;;;;;;;;;;;8BAIlD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAU;sCAEV,cAAA,6LAAC,qMAAA,CAAA,QAAK;gCAAC,MAAM;;;;;;;;;;;sCAEf,6LAAC;4BACC,SAAS,IAAM,SAAS,KAAK,EAAE;4BAC/B,WAAU;sCAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;gCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1B;GA3GwB;KAAA", "debugId": null}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/supabase/todo-app/src/components/TodoList.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Todo } from '@/types/todo'\nimport TodoItem from './TodoItem'\n\ninterface TodoListProps {\n  todos: Todo[]\n  onUpdate: (id: string, updates: Partial<Todo>) => void\n  onDelete: (id: string) => void\n}\n\ntype FilterType = 'all' | 'active' | 'completed'\n\nexport default function TodoList({ todos, onUpdate, onDelete }: TodoListProps) {\n  const [filter, setFilter] = useState<FilterType>('all')\n\n  const filteredTodos = todos.filter(todo => {\n    switch (filter) {\n      case 'active':\n        return !todo.completed\n      case 'completed':\n        return todo.completed\n      default:\n        return true\n    }\n  })\n\n  const activeTodosCount = todos.filter(todo => !todo.completed).length\n  const completedTodosCount = todos.filter(todo => todo.completed).length\n\n  return (\n    <div className=\"space-y-4\">\n      {/* 统计信息 */}\n      <div className=\"bg-white p-4 rounded-lg shadow-sm border border-gray-200\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"text-sm text-gray-600\">\n            总计: {todos.length} | 待完成: {activeTodosCount} | 已完成: {completedTodosCount}\n          </div>\n          \n          {/* 过滤器 */}\n          <div className=\"flex gap-1\">\n            <button\n              onClick={() => setFilter('all')}\n              className={`px-3 py-1 text-sm rounded-md transition-colors ${\n                filter === 'all'\n                  ? 'bg-blue-500 text-white'\n                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n              }`}\n            >\n              全部\n            </button>\n            <button\n              onClick={() => setFilter('active')}\n              className={`px-3 py-1 text-sm rounded-md transition-colors ${\n                filter === 'active'\n                  ? 'bg-blue-500 text-white'\n                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n              }`}\n            >\n              待完成\n            </button>\n            <button\n              onClick={() => setFilter('completed')}\n              className={`px-3 py-1 text-sm rounded-md transition-colors ${\n                filter === 'completed'\n                  ? 'bg-blue-500 text-white'\n                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n              }`}\n            >\n              已完成\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* 待办事项列表 */}\n      <div className=\"space-y-3\">\n        {filteredTodos.length === 0 ? (\n          <div className=\"text-center py-8 text-gray-500\">\n            {filter === 'all' && '暂无待办事项'}\n            {filter === 'active' && '暂无待完成的事项'}\n            {filter === 'completed' && '暂无已完成的事项'}\n          </div>\n        ) : (\n          filteredTodos.map(todo => (\n            <TodoItem\n              key={todo.id}\n              todo={todo}\n              onUpdate={onUpdate}\n              onDelete={onDelete}\n            />\n          ))\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAce,SAAS,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAiB;;IAC3E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IAEjD,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA;QACjC,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,KAAK,SAAS;YACxB,KAAK;gBACH,OAAO,KAAK,SAAS;YACvB;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,MAAM,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS,EAAE,MAAM;IACrE,MAAM,sBAAsB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;IAEvE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCAAwB;gCAChC,MAAM,MAAM;gCAAC;gCAAS;gCAAiB;gCAAS;;;;;;;sCAIvD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAC,+CAA+C,EACzD,WAAW,QACP,2BACA,+CACJ;8CACH;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAC,+CAA+C,EACzD,WAAW,WACP,2BACA,+CACJ;8CACH;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAC,+CAA+C,EACzD,WAAW,cACP,2BACA,+CACJ;8CACH;;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;0BACZ,cAAc,MAAM,KAAK,kBACxB,6LAAC;oBAAI,WAAU;;wBACZ,WAAW,SAAS;wBACpB,WAAW,YAAY;wBACvB,WAAW,eAAe;;;;;;2BAG7B,cAAc,GAAG,CAAC,CAAA,qBAChB,6LAAC,iIAAA,CAAA,UAAQ;wBAEP,MAAM;wBACN,UAAU;wBACV,UAAU;uBAHL,KAAK,EAAE;;;;;;;;;;;;;;;;AAU1B;GAnFwB;KAAA", "debugId": null}}, {"offset": {"line": 804, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/supabase/todo-app/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useTodos } from '@/hooks/useTodos'\nimport AddTodoForm from '@/components/AddTodoForm'\nimport TodoList from '@/components/TodoList'\nimport { Trash2, RefreshCw } from 'lucide-react'\n\nexport default function Home() {\n  const {\n    todos,\n    loading,\n    error,\n    addTodo,\n    updateTodo,\n    deleteTodo,\n    deleteCompletedTodos,\n    refetch\n  } = useTodos()\n\n  const handleAddTodo = async (input: { title: string; description?: string }) => {\n    try {\n      await addTodo(input)\n    } catch (err) {\n      // 错误已在hook中处理\n    }\n  }\n\n  const handleUpdateTodo = async (id: string, updates: any) => {\n    try {\n      await updateTodo(id, updates)\n    } catch (err) {\n      // 错误已在hook中处理\n    }\n  }\n\n  const handleDeleteTodo = async (id: string) => {\n    if (confirm('确定要删除这个待办事项吗？')) {\n      try {\n        await deleteTodo(id)\n      } catch (err) {\n        // 错误已在hook中处理\n      }\n    }\n  }\n\n  const handleDeleteCompleted = async () => {\n    const completedCount = todos.filter(todo => todo.completed).length\n    if (completedCount === 0) {\n      alert('没有已完成的待办事项')\n      return\n    }\n\n    if (confirm(`确定要删除 ${completedCount} 个已完成的待办事项吗？`)) {\n      try {\n        await deleteCompletedTodos()\n      } catch (err) {\n        // 错误已在hook中处理\n      }\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">加载中...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-4xl mx-auto px-4 py-8\">\n        {/* 头部 */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-2\">待办事项管理</h1>\n          <p className=\"text-gray-600\">管理你的日常任务，提高工作效率</p>\n        </div>\n\n        {/* 错误提示 */}\n        {error && (\n          <div className=\"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\">\n            <p className=\"text-red-600\">{error}</p>\n            <button\n              onClick={refetch}\n              className=\"mt-2 text-red-500 hover:text-red-700 underline\"\n            >\n              重试\n            </button>\n          </div>\n        )}\n\n        {/* 操作按钮 */}\n        <div className=\"mb-6 flex gap-2 justify-end\">\n          <button\n            onClick={refetch}\n            className=\"flex items-center gap-1 px-3 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors\"\n          >\n            <RefreshCw size={16} />\n            刷新\n          </button>\n          <button\n            onClick={handleDeleteCompleted}\n            className=\"flex items-center gap-1 px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors\"\n          >\n            <Trash2 size={16} />\n            清除已完成\n          </button>\n        </div>\n\n        {/* 添加待办事项表单 */}\n        <div className=\"mb-6\">\n          <AddTodoForm onAdd={handleAddTodo} />\n        </div>\n\n        {/* 待办事项列表 */}\n        <TodoList\n          todos={todos}\n          onUpdate={handleUpdateTodo}\n          onDelete={handleDeleteTodo}\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EACJ,KAAK,EACL,OAAO,EACP,KAAK,EACL,OAAO,EACP,UAAU,EACV,UAAU,EACV,oBAAoB,EACpB,OAAO,EACR,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IAEX,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,QAAQ;QAChB,EAAE,OAAO,KAAK;QACZ,cAAc;QAChB;IACF;IAEA,MAAM,mBAAmB,OAAO,IAAY;QAC1C,IAAI;YACF,MAAM,WAAW,IAAI;QACvB,EAAE,OAAO,KAAK;QACZ,cAAc;QAChB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,QAAQ,kBAAkB;YAC5B,IAAI;gBACF,MAAM,WAAW;YACnB,EAAE,OAAO,KAAK;YACZ,cAAc;YAChB;QACF;IACF;IAEA,MAAM,wBAAwB;QAC5B,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;QAClE,IAAI,mBAAmB,GAAG;YACxB,MAAM;YACN;QACF;QAEA,IAAI,QAAQ,CAAC,MAAM,EAAE,eAAe,YAAY,CAAC,GAAG;YAClD,IAAI;gBACF,MAAM;YACR,EAAE,OAAO,KAAK;YACZ,cAAc;YAChB;QACF;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;gBAI9B,uBACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAC7B,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAOL,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,mNAAA,CAAA,YAAS;oCAAC,MAAM;;;;;;gCAAM;;;;;;;sCAGzB,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,6MAAA,CAAA,SAAM;oCAAC,MAAM;;;;;;gCAAM;;;;;;;;;;;;;8BAMxB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,oIAAA,CAAA,UAAW;wBAAC,OAAO;;;;;;;;;;;8BAItB,6LAAC,iIAAA,CAAA,UAAQ;oBACP,OAAO;oBACP,UAAU;oBACV,UAAU;;;;;;;;;;;;;;;;;AAKpB;GAvHwB;;QAUlB,2HAAA,CAAA,WAAQ;;;KAVU", "debugId": null}}]}